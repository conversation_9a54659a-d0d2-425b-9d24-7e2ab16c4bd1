//
//  MessageManager.swift
//  ChatAdvisor
//
//  Created by AI Assistant on 2025/01/29.
//

import Foundation
import OSLog
import Combine

private let logger = Logger(subsystem: "com.sanva.chatadvisor", category: "MessageManager")

/// 弱引用包装器
class WeakReference<T: AnyObject> {
    weak var value: T?

    init(_ value: T) {
        self.value = value
    }
}

/// 消息管理器 - 负责消息的CRUD操作和业务逻辑
/// 使用Actor模式确保线程安全
actor MessageManager: ObservableObject {
    // MARK: - Published Properties
    @MainActor @Published var messages: [ChatMessage] = []
    @MainActor @Published var isLoading: Bool = false
    @MainActor @Published var hasMoreMessages: Bool = true

    // MARK: - Private Properties
    private let chatRepository: ChatRepositoryProtocol
    private let messageRepository: MessageRepositoryProtocol
    private let cacheManager: CacheManager<ChatMessage>
    private var currentChatId: String = ""
    private let pageSize: Int = 20

    // MARK: - 新增：状态管理相关属性
    private var messageStates: [String: MessageState] = [:]
    private var recoveryInfos: [String: MessageRecoveryInfo] = [:]
    private var stateChangeListeners: [WeakReference<MessageStateChangeListener>] = []
    private let stateUpdateQueue = DispatchQueue(label: "com.sanva.chatadvisor.messagestate", qos: .userInitiated)
    
    // MARK: - Initialization
    init(chatRepository: ChatRepositoryProtocol, 
         messageRepository: MessageRepositoryProtocol,
         cacheManager: CacheManager<ChatMessage>) {
        self.chatRepository = chatRepository
        self.messageRepository = messageRepository
        self.cacheManager = cacheManager
    }
    
    // MARK: - Public Methods
    
    /// 设置当前聊天ID并加载消息
    func setChatId(_ chatId: String) async {
        guard chatId != currentChatId else { return }
        
        currentChatId = chatId
        messages.removeAll()
        hasMoreMessages = true
        
        await loadInitialMessages()
    }
    
    /// 加载初始消息
    func loadInitialMessages() async {
        guard !currentChatId.isEmpty else { return }
        
        isLoading = true
        
        do {
            let loadedMessages = try await messageRepository.fetchMessages(
                chatId: currentChatId,
                limit: pageSize,
                before: []
            )
            
            messages = loadedMessages
            hasMoreMessages = loadedMessages.count == pageSize
            
            logger.info("加载初始消息成功: \(loadedMessages.count) 条")
        } catch {
            logger.error("加载初始消息失败: \(error.localizedDescription)")
        }
        
        isLoading = false
    }
    
    /// 加载更多消息（分页）
    func loadMoreMessages() async {
        guard !isLoading && hasMoreMessages && !currentChatId.isEmpty else { return }
        
        isLoading = true
        
        do {
            let moreMessages = try await messageRepository.fetchMessages(
                chatId: currentChatId,
                limit: pageSize,
                before: messages
            )
            
            // 将新消息插入到列表开头（因为是历史消息）
            messages.insert(contentsOf: moreMessages, at: 0)
            hasMoreMessages = moreMessages.count == pageSize
            
            logger.info("加载更多消息成功: \(moreMessages.count) 条")
        } catch {
            logger.error("加载更多消息失败: \(error.localizedDescription)")
        }
        
        isLoading = false
    }
    
    /// 添加新消息（线程安全版本）
    func addMessage(_ message: ChatMessage) async {
        // 检查消息是否已存在
        let currentMessages = await getCurrentMessages()
        guard !currentMessages.contains(where: { $0.id == message.id }) else {
            logger.warning("消息已存在，跳过添加: \(message.id)")
            return
        }

        // 设置初始状态
        let initialState: MessageState = message.role == .user ? .draft : .receiving
        await updateMessageStateInternal(message.id, to: initialState)

        // 添加到内存列表（主线程操作）
        await MainActor.run {
            self.messages.append(message)
        }

        // 异步保存到数据库
        do {
            try await messageRepository.saveMessage(message)
            logger.debug("消息保存成功: \(message.id)")

            // 更新状态为已保存
            let newState: MessageState = message.role == .user ? .sent : .receiving
            await updateMessageStateInternal(message.id, to: newState)

        } catch {
            logger.error("消息保存失败: \(error.localizedDescription)")

            // 保存失败时从内存中移除并标记为失败
            await MainActor.run {
                self.messages.removeAll { $0.id == message.id }
            }
            await updateMessageStateInternal(message.id, to: .failed)

            // 保存恢复信息
            let recoveryInfo = MessageRecoveryInfo(
                messageId: message.id,
                chatId: message.chatId,
                state: .failed,
                content: message.content
            )
            await saveRecoveryInfoInternal(recoveryInfo)
        }
    }
    
    /// 更新消息（线程安全版本）
    func updateMessage(_ message: ChatMessage) async {
        // 更新内存中的消息（主线程操作）
        await MainActor.run {
            if let index = self.messages.firstIndex(where: { $0.id == message.id }) {
                self.messages[index] = message
            }
        }

        // 异步更新数据库
        do {
            try await messageRepository.updateMessage(message)
            logger.debug("消息更新成功: \(message.id)")

            // 如果消息已完成，更新状态
            if message.isComplete {
                await updateMessageStateInternal(message.id, to: .completed)
                await removeRecoveryInfoInternal(message.id)
            }

        } catch {
            logger.error("消息更新失败: \(error.localizedDescription)")
            await updateMessageStateInternal(message.id, to: .failed)

            // 保存恢复信息
            let recoveryInfo = MessageRecoveryInfo(
                messageId: message.id,
                chatId: message.chatId,
                state: .failed,
                content: message.content
            )
            await saveRecoveryInfoInternal(recoveryInfo)
        }
    }
    
    /// 删除消息
    func deleteMessage(id: String) async {
        // 从内存中移除
        messages.removeAll { $0.id == id }
        
        // 异步从数据库删除
        do {
            try await messageRepository.deleteMessage(id: id)
            logger.debug("消息删除成功: \(id)")
        } catch {
            logger.error("消息删除失败: \(error.localizedDescription)")
        }
    }
    
    /// 批量添加消息
    func addMessages(_ newMessages: [ChatMessage]) async {
        let uniqueMessages = newMessages.filter { newMessage in
            !messages.contains { $0.id == newMessage.id }
        }
        
        guard !uniqueMessages.isEmpty else { return }
        
        // 添加到内存列表
        messages.append(contentsOf: uniqueMessages)
        
        // 异步批量保存到数据库
        do {
            try await messageRepository.batchSaveMessages(uniqueMessages)
            logger.debug("批量消息保存成功: \(uniqueMessages.count) 条")
        } catch {
            logger.error("批量消息保存失败: \(error.localizedDescription)")
            // 保存失败时从内存中移除
            for message in uniqueMessages {
                messages.removeAll { $0.id == message.id }
            }
        }
    }
    
    /// 获取指定角色的消息
    func getMessages(for role: Role) -> [ChatMessage] {
        return messages.filter { $0.role == role }
    }
    
    /// 获取用户消息（排除系统消息）
    func getUserMessages() -> [ChatMessage] {
        return messages.filter { $0.role != .system }
    }
    
    /// 获取最后一条消息
    func getLastMessage() -> ChatMessage? {
        return messages.last
    }
    
    /// 获取最后一条用户消息
    func getLastUserMessage() -> ChatMessage? {
        return messages.last { $0.role == .user }
    }
    
    /// 获取最后一条助手消息
    func getLastAssistantMessage() -> ChatMessage? {
        return messages.last { $0.role == .assistant }
    }
    
    /// 检查是否有未完成的消息
    func hasIncompleteMessage() -> Bool {
        return messages.contains { !$0.isComplete }
    }
    
    /// 获取未完成的消息
    func getIncompleteMessages() -> [ChatMessage] {
        return messages.filter { !$0.isComplete }
    }
    
    /// 标记消息为完成状态
    func markMessageAsComplete(id: String) async {
        guard let index = messages.firstIndex(where: { $0.id == id }) else { return }
        
        var message = messages[index]
        message.isComplete = true
        messages[index] = message
        
        // 异步更新数据库
        await updateMessage(message)
    }
    
    /// 清空当前聊天的所有消息
    func clearMessages() async {
        let chatId = currentChatId

        await MainActor.run {
            self.messages.removeAll()
        }

        // 清空状态信息
        messageStates.removeAll()
        recoveryInfos.removeAll()

        guard !chatId.isEmpty else { return }

        // 异步从数据库删除
        do {
            try await messageRepository.deleteAllMessages(chatId: chatId)
            logger.info("清空聊天消息成功: \(chatId)")
        } catch {
            logger.error("清空聊天消息失败: \(error.localizedDescription)")
        }
    }

    // MARK: - 状态管理方法

    /// 更新消息状态
    func updateMessageState(_ messageId: String, to newState: MessageState) async throws {
        await updateMessageStateInternal(messageId, to: newState)
    }

    /// 内部状态更新方法
    private func updateMessageStateInternal(_ messageId: String, to newState: MessageState) async {
        let oldState = messageStates[messageId] ?? .draft

        // 验证状态转换
        guard oldState.canTransitionTo(newState) else {
            logger.warning("无效的状态转换: \(messageId) from \(oldState) to \(newState)")
            return
        }

        messageStates[messageId] = newState

        // 通知状态变更
        let event = MessageStateChangeEvent(
            messageId: messageId,
            chatId: currentChatId,
            from: oldState,
            to: newState
        )

        await notifyStateChange(event)
        logger.debug("消息状态更新: \(messageId) \(oldState) -> \(newState)")
    }

    /// 获取消息状态
    func getMessageState(_ messageId: String) async -> MessageState? {
        return messageStates[messageId]
    }

    /// 保存恢复信息
    private func saveRecoveryInfoInternal(_ info: MessageRecoveryInfo) async {
        recoveryInfos[info.messageId] = info
        logger.debug("保存恢复信息: \(info.messageId)")
    }

    /// 移除恢复信息
    private func removeRecoveryInfoInternal(_ messageId: String) async {
        recoveryInfos.removeValue(forKey: messageId)
        logger.debug("移除恢复信息: \(messageId)")
    }

    /// 获取所有待恢复的消息
    func getAllPendingRecoveries() async -> [MessageRecoveryInfo] {
        return Array(recoveryInfos.values).filter { $0.canRetry }
    }

    /// 添加状态变更监听器
    func addStateChangeListener(_ listener: MessageStateChangeListener) async {
        let weakRef = WeakReference(listener)
        stateChangeListeners.append(weakRef)
    }

    /// 通知状态变更
    private func notifyStateChange(_ event: MessageStateChangeEvent) async {
        // 清理已释放的监听器
        stateChangeListeners.removeAll { $0.value == nil }

        // 通知所有监听器
        for weakRef in stateChangeListeners {
            weakRef.value?.messageStateDidChange(event)
        }
    }

    /// 获取当前消息列表（线程安全）
    private func getCurrentMessages() async -> [ChatMessage] {
        return await MainActor.run { self.messages }
    }
    
    /// 搜索消息
    func searchMessages(keyword: String) async -> [ChatMessage] {
        guard !keyword.isEmpty else { return [] }
        
        do {
            let results = try await messageRepository.searchMessages(keyword: keyword, chatId: currentChatId)
            logger.debug("消息搜索完成: \(results.count) 条结果")
            return results
        } catch {
            logger.error("消息搜索失败: \(error.localizedDescription)")
            return []
        }
    }
    
    /// 获取消息统计信息
    func getMessageStats() -> (total: Int, userMessages: Int, assistantMessages: Int) {
        let userCount = messages.filter { $0.role == .user }.count
        let assistantCount = messages.filter { $0.role == .assistant }.count
        return (total: messages.count, userMessages: userCount, assistantMessages: assistantCount)
    }
}
