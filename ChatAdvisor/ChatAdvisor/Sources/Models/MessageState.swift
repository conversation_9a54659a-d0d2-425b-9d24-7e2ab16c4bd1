import Foundation
import OSLog

private let logger = Logger(subsystem: "com.sanva.chatadvisor", category: "MessageState")

/// 消息状态枚举 - 定义消息的完整生命周期
enum MessageState: String, CaseIterable, Codable {
    case draft = "draft"                    // 草稿状态
    case sending = "sending"                // 发送中
    case sent = "sent"                      // 已发送
    case receiving = "receiving"            // 接收中（AI回复）
    case streaming = "streaming"            // 流式接收中
    case completed = "completed"            // 完成
    case failed = "failed"                  // 失败
    case retrying = "retrying"              // 重试中
    case recovered = "recovered"            // 已恢复
    
    /// 是否为最终状态
    var isFinalState: Bool {
        switch self {
        case .completed, .failed:
            return true
        default:
            return false
        }
    }
    
    /// 是否为进行中状态
    var isInProgress: Bool {
        switch self {
        case .sending, .receiving, .streaming, .retrying:
            return true
        default:
            return false
        }
    }
    
    /// 是否可以重试
    var canRetry: Bool {
        switch self {
        case .failed:
            return true
        default:
            return false
        }
    }
    
    /// 状态转换验证
    func canTransitionTo(_ newState: MessageState) -> Bool {
        switch (self, newState) {
        case (.draft, .sending):
            return true
        case (.sending, .sent), (.sending, .failed):
            return true
        case (.sent, .receiving):
            return true
        case (.receiving, .streaming), (.receiving, .failed):
            return true
        case (.streaming, .completed), (.streaming, .failed):
            return true
        case (.failed, .retrying):
            return true
        case (.retrying, .sending), (.retrying, .receiving), (.retrying, .streaming):
            return true
        case (_, .recovered):
            return true
        default:
            return false
        }
    }
}

/// 消息恢复信息
struct MessageRecoveryInfo: Codable {
    let messageId: String
    let chatId: String
    let lastKnownState: MessageState
    let partialContent: String?
    let timestamp: Date
    let retryCount: Int
    let maxRetries: Int
    
    init(messageId: String, chatId: String, state: MessageState, content: String? = nil, retryCount: Int = 0, maxRetries: Int = 3) {
        self.messageId = messageId
        self.chatId = chatId
        self.lastKnownState = state
        self.partialContent = content
        self.timestamp = Date()
        self.retryCount = retryCount
        self.maxRetries = maxRetries
    }
    
    /// 是否可以继续重试
    var canRetry: Bool {
        return retryCount < maxRetries && lastKnownState.canRetry
    }
    
    /// 创建下一次重试的恢复信息
    func nextRetry() -> MessageRecoveryInfo {
        return MessageRecoveryInfo(
            messageId: messageId,
            chatId: chatId,
            state: .retrying,
            content: partialContent,
            retryCount: retryCount + 1,
            maxRetries: maxRetries
        )
    }
}

/// 消息状态变更事件
struct MessageStateChangeEvent {
    let messageId: String
    let chatId: String
    let oldState: MessageState
    let newState: MessageState
    let timestamp: Date
    let context: [String: Any]?
    
    init(messageId: String, chatId: String, from oldState: MessageState, to newState: MessageState, context: [String: Any]? = nil) {
        self.messageId = messageId
        self.chatId = chatId
        self.oldState = oldState
        self.newState = newState
        self.timestamp = Date()
        self.context = context
    }
}

/// 消息状态管理器协议
protocol MessageStateManagerProtocol {
    func updateMessageState(_ messageId: String, to newState: MessageState) async throws
    func getMessageState(_ messageId: String) async -> MessageState?
    func getRecoveryInfo(_ messageId: String) async -> MessageRecoveryInfo?
    func saveRecoveryInfo(_ info: MessageRecoveryInfo) async throws
    func removeRecoveryInfo(_ messageId: String) async throws
    func getAllPendingRecoveries() async -> [MessageRecoveryInfo]
}

/// 消息状态变更监听器协议
protocol MessageStateChangeListener: AnyObject {
    func messageStateDidChange(_ event: MessageStateChangeEvent)
}
